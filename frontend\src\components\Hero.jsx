import { motion } from "framer-motion";

export default function Hero() {
  return (
    <section className="bg-gradient-to-r from-indigo-100 via-white to-indigo-50">
      <div className="container mx-auto px-6 py-20 flex flex-col md:flex-row items-center gap-10">
        
        {/* Left Content */}
        <motion.div
          initial={{ x: -80, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          className="flex-1"
        >
          <h1 className="text-4xl md:text-6xl font-bold text-gray-800 leading-tight">
            Worship Essentials <br />
            <span className="text-indigo-600">Delivered with Care</span>
          </h1>
          <p className="mt-4 text-gray-600 text-lg">
            Discover high-quality worship items for your rituals, festivals, and spiritual journey.
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="mt-6 px-6 py-3 bg-indigo-600 text-white rounded-xl shadow hover:bg-indigo-700 transition"
          >
            Shop Now
          </motion.button>
        </motion.div>

        {/* Right Image */}
        <motion.div
          initial={{ x: 80, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          className="flex-1"
        >
          <div className="bg-gradient-to-br from-indigo-200 to-purple-300 rounded-2xl shadow-lg hover:scale-105 transition-transform duration-500 h-80 flex items-center justify-center">
            <div className="text-center text-indigo-800">
              <div className="text-6xl mb-4">🕉️</div>
              <p className="text-lg font-semibold">Worship Items</p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
