import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import hero from "../assets/hero.png";

export default function Hero() {
  return (
    <section className="relative bg-gradient-to-r from-yellow-50 via-white to-yellow-50 overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('https://www.toptal.com/designers/subtlepatterns/patterns/pw_maze_white.png')] opacity-15" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 py-20 lg:py-28 flex flex-col lg:flex-row items-center gap-12">
        {/* Left Content */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="flex-1 text-center lg:text-left space-y-6"
        >
          <motion.h1
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1, delay: 0.2 }}
            className="text-4xl md:text-5xl lg:text-6xl font-extrabold text-gray-800 leading-tight"
          >
            Discover Sacred & Divine{" "}
            <span className="text-yellow-600">Worship Essentials</span>
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.4 }}
            className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto lg:mx-0"
          >
            Bring spirituality closer with premium worship products. Handpicked
            essentials crafted with devotion and care — delivered at your door.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.6 }}
            className="flex flex-col sm:flex-row justify-center lg:justify-start gap-4 pt-4"
          >
            <Link
              to="/products"
              className="px-6 py-3 rounded-full bg-yellow-600 text-white font-semibold shadow-lg hover:bg-yellow-700 transition-transform hover:scale-105"
            >
              Shop Now
            </Link>
            <Link
              to="/about"
              className="px-6 py-3 rounded-full bg-white text-yellow-700 font-semibold border border-yellow-600 hover:bg-yellow-50 transition-transform hover:scale-105"
            >
              Learn More
            </Link>
          </motion.div>
        </motion.div>

        {/* Right Content - Image */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 0.8 }}
          className="flex-1 relative"
        >
          <motion.img
            src={hero}
            alt="Worship Items"
            className="w-full max-w-md mx-auto drop-shadow-2xl rounded-2xl"
            animate={{ y: [0, -15, 0] }}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
          />
          {/* Glow effect */}
          <motion.div
            className="absolute -z-10 top-10 left-10 w-72 h-72 bg-yellow-200 rounded-full mix-blend-multiply filter blur-3xl opacity-40"
            animate={{ scale: [1, 1.2, 1], opacity: [0.4, 0.6, 0.4] }}
            transition={{ duration: 5, repeat: Infinity, ease: "easeInOut" }}
          />
        </motion.div>
      </div>
    </section>
  );
}
