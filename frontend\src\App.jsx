// src/App.jsx
import { BrowserRouter as Router, Routes, Route, useLocation } from "react-router-dom";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";

import Home from "./pages/Home";
import Products from "./pages/Products";
import ProductDetail from "./pages/ProductDetail";
import Login from "./pages/Login";
import Signup from "./pages/Signup";

import logo from "./assets/logo.png";

function AppLayout() {
  const location = useLocation();
  const authRoutes = ["/login", "/signup"];
  const isAuthRoute = authRoutes.includes(location.pathname);

  return (
    <div className="font-sans bg-gray-50 min-h-screen flex flex-col">
      {isAuthRoute ? (
        <div className="w-full p-4 flex items-center border-b bg-white shadow-sm">
          <img
            src={logo}
            alt="Worship Logo"
            className="h-16 md:h-20 object-contain cursor-pointer"
            onClick={() => (window.location.href = "/")}
          />
        </div>
      ) : (
        <Navbar />
      )}

      <main className="flex-grow">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/products" element={<Products />} />
          <Route path="/products/:id" element={<ProductDetail />} />
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
        </Routes>
      </main>

      {!isAuthRoute && <Footer />}
    </div>
  );
}

export default function App() {
  return (
    <Router>
      <AppLayout />
    </Router>
  );
}
