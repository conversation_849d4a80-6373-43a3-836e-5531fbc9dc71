// src/components/ProductCard.jsx
import { Link } from "react-router-dom";
import { motion } from "framer-motion";

export default function ProductCard({ product }) {
  return (
    <Link to={`/products/${product.id}`}>
      <motion.div
        whileHover={{ scale: 1.05 }}
        className="bg-white rounded-2xl shadow-md hover:shadow-xl transition p-4 cursor-pointer"
      >
        <img
          src={product.images[0]}
          alt={product.name}
          className="h-56 w-full object-cover rounded-md group-hover:scale-105 transition-transform duration-300"
        />
        <h3 className="text-lg font-semibold mt-4 text-gray-700">{product.name}</h3>
        <p className="text-amber-600 font-bold">₹{product.price}</p>
      </motion.div>
    </Link>
  );
}
