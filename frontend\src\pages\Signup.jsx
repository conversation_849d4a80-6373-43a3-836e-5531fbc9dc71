import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>nvelope, FaLock } from "react-icons/fa";
import { motion } from "framer-motion";

export default function SignupPage() {
  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Signup form submitted");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-yellow-100 via-orange-50 to-yellow-200 overflow-hidden">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
        className="bg-white shadow-2xl rounded-2xl w-full max-w-md p-10 relative overflow-hidden"
      >
        {/* Glow decorations */}
        <div className="absolute -top-16 -right-16 w-40 h-40 bg-yellow-300 rounded-full opacity-30 blur-3xl"></div>
        <div className="absolute -bottom-16 -left-16 w-40 h-40 bg-orange-300 rounded-full opacity-30 blur-3xl"></div>

        {/* Heading */}
        <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">
          🙏 Create Your <span className="text-yellow-600">Account</span>
        </h2>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6 relative z-10">
          {/* Full Name */}
          <div className="relative">
            <FaUser className="absolute left-4 top-3 text-gray-400" />
            <input
              type="text"
              placeholder="Full Name"
              className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400 transition"
              required
            />
          </div>

          {/* Email */}
          <div className="relative">
            <FaRegEnvelope className="absolute left-4 top-3 text-gray-400" />
            <input
              type="email"
              placeholder="Email Address"
              className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400 transition"
              required
            />
          </div>

          {/* Password */}
          <div className="relative">
            <FaLock className="absolute left-4 top-3 text-gray-400" />
            <input
              type="password"
              placeholder="Password"
              className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400 transition"
              required
            />
          </div>

          {/* Confirm Password */}
          <div className="relative">
            <FaLock className="absolute left-4 top-3 text-gray-400" />
            <input
              type="password"
              placeholder="Confirm Password"
              className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400 transition"
              required
            />
          </div>

          {/* Submit */}
          <motion.button
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            type="submit"
            className="w-full py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition"
          >
            Create Account
          </motion.button>

          {/* Already have account */}
          <p className="text-sm text-center text-gray-600 mt-4">
            Already have an account?{" "}
            <a href="/login" className="text-orange-600 hover:underline">
              Sign In
            </a>
          </p>
        </form>
      </motion.div>
    </div>
  );
}
