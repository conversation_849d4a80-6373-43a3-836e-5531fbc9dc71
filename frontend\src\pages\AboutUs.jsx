// src/pages/AboutUs.jsx
import { motion } from "framer-motion";

export default function AboutUs() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
      className="bg-gradient-to-b from-amber-50 via-white to-amber-50 min-h-screen"
    >
      {/* Hero Section */}
      <div className="relative h-96 flex items-center justify-center bg-cover bg-center rounded-b-3xl shadow-lg"
           style={{ backgroundImage: 'url("https://images.unsplash.com/photo-1606312619070-9de36f2e6b78?auto=format&fit=crop&w=1200&q=80")' }}>
        <div className="absolute inset-0 bg-amber-900/30 rounded-b-3xl"></div>
        <motion.h1
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 1 }}
          className="text-4xl md:text-5xl font-bold text-white text-center z-10"
        >
          About Our Worship Store
        </motion.h1>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-16 space-y-16">

        {/* Our Story Section */}
        <motion.div
          whileInView={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: 40 }}
          viewport={{ once: true }}
          className="flex flex-col md:flex-row items-center gap-12"
        >
          <img
            src="https://images.unsplash.com/photo-1617196037890-2a1b2bdbf8a0?auto=format&fit=crop&w=600&q=80"
            alt="Our Story"
            className="rounded-3xl shadow-2xl w-full md:w-1/2 object-cover"
          />
          <div className="md:w-1/2 space-y-4">
            <h2 className="text-3xl font-bold text-amber-800">Our Spiritual Journey</h2>
            <p className="text-gray-700">
              Founded with devotion, we curate spiritual products that connect
              your home to divine energy. Each item is handcrafted by skilled
              artisans, honoring tradition and rituals.
            </p>
            <p className="text-gray-700">
              Our mission is to provide a seamless spiritual experience,
              supporting authentic craftsmanship and eco-friendly materials.
            </p>
          </div>
        </motion.div>

        {/* Core Values Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-amber-800 mb-8">Our Core Values</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <motion.div whileHover={{ scale: 1.05 }} className="p-6 rounded-2xl shadow-lg hover:shadow-xl transition bg-white">
              <h3 className="text-xl font-semibold text-amber-600 mb-2">Handmade Devotion</h3>
              <p className="text-gray-700">
                Each product is made with love and spiritual intention.
              </p>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} className="p-6 rounded-2xl shadow-lg hover:shadow-xl transition bg-white">
              <h3 className="text-xl font-semibold text-amber-600 mb-2">Ritual Purpose</h3>
              <p className="text-gray-700">
                Products crafted for pooja, meditation, and sacred ceremonies.
              </p>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} className="p-6 rounded-2xl shadow-lg hover:shadow-xl transition bg-white">
              <h3 className="text-xl font-semibold text-amber-600 mb-2">Eco-Friendly</h3>
              <p className="text-gray-700">
                Sustainable materials respecting nature and tradition.
              </p>
            </motion.div>
          </div>
        </div>

        {/* Artisans / Team Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-amber-800 mb-8">Our Artisans</h2>
          <div className="grid sm:grid-cols-2 md:grid-cols-4 gap-8">
            {["Ramesh", "Sita", "Vikram", "Meera"].map((name, idx) => (
              <motion.div key={idx} whileHover={{ scale: 1.05, y: -5 }} className="p-4 rounded-xl shadow-lg hover:shadow-2xl transition bg-white">
                <img
                  src={`https://images.unsplash.com/photo-1603415526960-fbb07c6aa716?auto=format&fit=crop&w=400&q=80`}
                  alt={name}
                  className="w-full h-52 object-cover rounded-lg mb-4"
                />
                <h3 className="font-semibold text-gray-900">{name}</h3>
                <p className="text-gray-600">Craftsman</p>
              </motion.div>
            ))}
          </div>
        </div>

      </div>
    </motion.div>
  );
}
