import { useState } from "react";
import { FaRegEnvelope, FaLock } from "react-icons/fa";
import { motion } from "framer-motion";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleLogin = (e) => {
    e.preventDefault();
    console.log("Login attempt:", { email, password });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-yellow-100 via-orange-50 to-yellow-200">
      <motion.div
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="bg-white shadow-2xl rounded-2xl w-full max-w-md p-10 relative overflow-hidden"
      >
        {/* Sacred Glow Accent */}
        <div className="absolute -top-16 -right-16 w-40 h-40 bg-yellow-300 rounded-full opacity-30 blur-3xl"></div>
        <div className="absolute -bottom-16 -left-16 w-40 h-40 bg-orange-300 rounded-full opacity-30 blur-3xl"></div>

        {/* Brand / Logo */}
        <h2 className="text-3xl font-bold text-center text-gray-800">
          🙏 Worship<span className="text-yellow-600">Mart</span>
        </h2>
        <p className="text-center text-gray-500 mt-2 text-sm">
          Welcome back, continue your spiritual shopping ✨
        </p>

        {/* Login Form */}
        <form className="mt-8 space-y-5" onSubmit={handleLogin}>
          {/* Email */}
          <div className="relative">
            <FaRegEnvelope className="absolute left-4 top-3 text-gray-400" />
            <input
              type="email"
              placeholder="Enter your email"
              className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400 transition"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>

          {/* Password */}
          <div className="relative">
            <FaLock className="absolute left-4 top-3 text-gray-400" />
            <input
              type="password"
              placeholder="Enter your password"
              className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400 transition"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>

          {/* Submit Button */}
          <motion.button
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            type="submit"
            className="w-full py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition"
          >
            Login
          </motion.button>
        </form>

        {/* Links */}
        <div className="mt-6 text-center text-sm text-gray-500">
          <p>
            Don’t have an account?{" "}
            <a href="/signup" className="text-yellow-600 font-medium hover:underline">
              Sign Up
            </a>
          </p>
          <p className="mt-2">
            <a href="/forgot-password" className="text-orange-600 hover:underline">
              Forgot Password?
            </a>
          </p>
        </div>
      </motion.div>
    </div>
  );
}
