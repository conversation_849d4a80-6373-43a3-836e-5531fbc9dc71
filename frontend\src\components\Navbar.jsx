import { Link, NavLink } from "react-router-dom";
import { motion } from "framer-motion";
import { Menu, X } from "lucide-react";
import { useState } from "react";


export default function Navbar() {
  const [menuOpen, setMenuOpen] = useState(false);

  const navLinks = [
    { name: "Home", path: "/" },
    { name: "Products", path: "/products" },
    { name: "Cart", path: "/cart" },
    { name: "Profile", path: "/profile" },
  ];

  return (
    <motion.header
      initial={{ y: -80, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.7, ease: "easeOut" }}
      className="sticky top-0 z-50 bg-white/80 backdrop-blur-lg shadow-md"
    >
      <div className="max-w-7xl mx-auto flex items-center justify-between px-6 py-4">
        {/* Logo */}
        <Link to="/" className="flex items-center gap-2">
          <div className="text-2xl">🕉️</div>
          <span className="text-xl font-bold text-indigo-600 hover:text-indigo-700 transition-colors">
            Worship Store
          </span>
        </Link>

        {/* Desktop Menu */}
        <nav className="hidden md:flex items-center gap-8">
          {navLinks.map((link, index) => (
            <NavLink
              key={index}
              to={link.path}
              className={({ isActive }) =>
                `relative text-lg font-medium transition-colors ${
                  isActive ? "text-yellow-600" : "text-gray-700"
                } hover:text-yellow-600`
              }
            >
              {({ isActive }) => (
                <>
                  {link.name}
                  {isActive && (
                    <motion.div
                      layoutId="underline"
                      className="absolute left-0 -bottom-1 w-full h-[2px] bg-yellow-600 rounded-full"
                    />
                  )}
                </>
              )}
            </NavLink>
          ))}

          {/* Buttons */}
          <div className="flex gap-3">
            <Link
              to="/login"
              className="px-4 py-2 rounded-full border border-yellow-600 text-yellow-700 font-semibold hover:bg-yellow-50 transition"
            >
              Login
            </Link>
            <Link
              to="/signup"
              className="px-4 py-2 rounded-full bg-yellow-600 text-white font-semibold shadow-md hover:bg-yellow-700 transition"
            >
              Sign Up
            </Link>
          </div>
        </nav>

        {/* Mobile Menu Button */}
        <button
          onClick={() => setMenuOpen(!menuOpen)}
          className="md:hidden p-2 text-gray-700"
        >
          {menuOpen ? <X size={28} /> : <Menu size={28} />}
        </button>
      </div>

      {/* Mobile Dropdown */}
      {menuOpen && (
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: "auto", opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          transition={{ duration: 0.4 }}
          className="md:hidden bg-white shadow-lg px-6 py-4 space-y-4"
        >
          {navLinks.map((link, index) => (
            <NavLink
              key={index}
              to={link.path}
              onClick={() => setMenuOpen(false)}
              className="block text-lg font-medium text-gray-700 hover:text-yellow-600"
            >
              {link.name}
            </NavLink>
          ))}

          <div className="flex flex-col gap-3 pt-4">
            <Link
              to="/login"
              className="px-4 py-2 rounded-full border border-yellow-600 text-yellow-700 font-semibold hover:bg-yellow-50 transition text-center"
            >
              Login
            </Link>
            <Link
              to="/signup"
              className="px-4 py-2 rounded-full bg-yellow-600 text-white font-semibold shadow-md hover:bg-yellow-700 transition text-center"
            >
              Sign Up
            </Link>
          </div>
        </motion.div>
      )}
    </motion.header>
  );
}
