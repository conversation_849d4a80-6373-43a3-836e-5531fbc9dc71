// src/pages/ProductDetail.jsx
import { useParams } from "react-router-dom";
import { useState } from "react";
import { products } from "./Products"; // import product data

export default function ProductDetail() {
  const { id } = useParams();
  const product = products.find((p) => p.id === id);
  const [selectedImage, setSelectedImage] = useState(product?.images[0]);

  if (!product)
    return (
      <h2 className="text-center text-red-500 text-2xl mt-10 font-semibold">
        Product not found
      </h2>
    );

  return (
    <div className="max-w-6xl mx-auto px-6 py-12">
      <div className="grid md:grid-cols-2 gap-12 items-start">
        {/* Left: Images */}
        <div>
          <img
            src={selectedImage}
            alt={product.name}
            className="w-full h-96 object-cover rounded-3xl shadow-lg"
          />
          <div className="flex gap-4 mt-4">
            {product.images.map((img, idx) => (
              <img
                key={idx}
                src={img}
                alt={`thumbnail-${idx}`}
                onClick={() => setSelectedImage(img)}
                className={`w-20 h-20 rounded-lg cursor-pointer border-2 ${
                  selectedImage === img
                    ? "border-amber-500 scale-105"
                    : "border-gray-200 hover:border-amber-400"
                } transition-all`}
              />
            ))}
          </div>
        </div>

        {/* Right: Product Info */}
        <div className="flex flex-col">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {product.name}
          </h1>
          <p className="text-lg text-gray-700 mb-4">{product.description}</p>
          <p className="text-3xl font-extrabold text-amber-600 mb-6">
            ₹{product.price}
          </p>

          <button className="bg-amber-600 hover:bg-amber-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg transition">
            Add to Cart 🛒
          </button>
        </div>
      </div>
    </div>
  );
}
