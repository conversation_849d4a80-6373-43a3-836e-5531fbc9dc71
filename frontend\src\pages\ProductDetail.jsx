// src/pages/ProductDetail.jsx
import { use<PERSON>ara<PERSON>, <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import { useState } from "react";

// Sample worship products
const products = [
  {
    id: "1",
    name: "Brass Pooja Thali Set",
    price: 799,
    description:
      "Handcrafted brass pooja thali with diya, bell, kumkum holder. Perfect for daily pooja rituals.",
    images: [
      "https://images.unsplash.com/photo-1606312619070-9de36f2e6b78?auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1607522370275-f14206abe5d2?auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1617196037890-2a1b2bdbf8a0?auto=format&fit=crop&w=800&q=80",
    ],
    ritualUse: [
      "Daily pooja and festive rituals",
      "Decorative and functional",
      "Eco-friendly and long-lasting",
    ],
  },
  {
    id: "2",
    name: "Ganesh Idol",
    price: 1299,
    description:
      "Elegant Ganesh idol made of eco-friendly marble. Ideal for home temple and Ganesh <PERSON>.",
    images: [
      "https://images.unsplash.com/photo-1598514982903-1c0a219c2f3c?auto=format&fit=crop&w=800&q=80",
    ],
    ritualUse: [
      "Install in home temple",
      "Blessing for prosperity",
      "Handcrafted with care",
    ],
  },
];

export default function ProductDetail() {
  const { id } = useParams();
  const product = products.find((p) => p.id === id);
  const [selectedImage, setSelectedImage] = useState(product?.images[0]);
  const [quantity, setQuantity] = useState(1);

  if (!product)
    return (
      <h2 className="text-center text-red-500 text-2xl mt-10 font-semibold">
        Product not found
      </h2>
    );

  const related = products.filter((p) => p.id !== product.id);

  return (
    <motion.div
      initial={{ opacity: 0, y: 40 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="max-w-7xl mx-auto px-6 py-12"
    >
      {/* Main Product Section */}
      <div className="grid md:grid-cols-2 gap-12 items-start">
        {/* Left: Image Gallery */}
        <div className="flex flex-col items-center relative">
          {/* Main Image with Glow */}
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="relative group w-full max-w-md"
          >
            <img
              src={selectedImage}
              alt={product.name}
              className="rounded-3xl shadow-2xl w-full object-cover transition-transform duration-500 transform group-hover:scale-105"
            />
            {/* Subtle Glow Overlay */}
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-t from-amber-50/20 via-transparent to-transparent pointer-events-none"></div>
          </motion.div>

          {/* Thumbnails */}
          <div className="flex gap-4 mt-4 overflow-x-auto py-2">
            {product.images.map((img, idx) => (
              <motion.img
                key={idx}
                src={img}
                alt={`thumbnail-${idx}`}
                onClick={() => setSelectedImage(img)}
                whileHover={{ scale: 1.15, rotate: 1 }}
                className={`w-20 h-20 rounded-lg cursor-pointer border-2 transition-all duration-300 ${
                  selectedImage === img
                    ? "border-amber-500 scale-110 shadow-lg"
                    : "border-gray-200 hover:border-amber-400"
                }`}
              />
            ))}
          </div>
        </div>

        {/* Right: Product Info */}
        <div className="flex flex-col space-y-6">
          <h1 className="text-4xl font-bold text-gray-900 tracking-tight">
            {product.name}
          </h1>
          <p className="text-lg text-gray-700">{product.description}</p>

          {/* Ritual Use */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Ritual & Usage:
            </h3>
            <ul className="list-disc list-inside text-gray-600 space-y-1">
              {product.ritualUse.map((item, idx) => (
                <li key={idx}>{item}</li>
              ))}
            </ul>
          </div>

          {/* Price */}
          <p className="text-3xl font-extrabold text-amber-600">
            ₹{product.price}
          </p>

          {/* Quantity + Buttons */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:gap-4">
            <div className="flex items-center gap-3 mb-4 sm:mb-0">
              <span className="font-semibold text-gray-800">Quantity:</span>
              <input
                type="number"
                min="1"
                value={quantity}
                onChange={(e) => setQuantity(Number(e.target.value))}
                className="w-20 px-3 py-2 border rounded-lg text-center shadow-sm focus:outline-none focus:ring-2 focus:ring-amber-400"
              />
            </div>
            <div className="flex gap-4">
              <motion.button
                whileTap={{ scale: 0.95 }}
                className="flex-1 bg-amber-600 hover:bg-amber-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg transition-transform"
              >
                Add to Cart 🛒
              </motion.button>
              <motion.button
                whileTap={{ scale: 0.95 }}
                className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-900 px-6 py-3 rounded-xl font-semibold shadow transition-transform"
              >
                Buy Now ✨
              </motion.button>
            </div>
          </div>

          {/* Trust Badges */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 text-center text-gray-600">
            <div className="p-4 border rounded-xl shadow-sm hover:shadow-md">
              🔒 <span className="font-medium">Secure Checkout</span>
            </div>
            <div className="p-4 border rounded-xl shadow-sm hover:shadow-md">
              ✅ <span className="font-medium">Handmade Quality</span>
            </div>
            <div className="p-4 border rounded-xl shadow-sm hover:shadow-md">
              🚚 <span className="font-medium">Fast Delivery</span>
            </div>
          </div>
        </div>
      </div>

      {/* Related Products */}
      <div className="mt-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          You may also like
        </h2>
        <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-8">
          {related.map((item) => (
            <Link key={item.id} to={`/products/${item.id}`}>
              <motion.div
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
                className="bg-white rounded-2xl shadow-md overflow-hidden hover:shadow-xl transition"
              >
                <img
                  src={item.images[0]}
                  alt={item.name}
                  className="h-56 w-full object-cover"
                />
                <div className="p-4">
                  <h3 className="font-semibold text-lg text-gray-800 hover:text-amber-600 transition">
                    {item.name}
                  </h3>
                  <p className="text-amber-600 font-bold mt-2">₹{item.price}</p>
                </div>
              </motion.div>
            </Link>
          ))}
        </div>
      </div>
    </motion.div>
  );
}
