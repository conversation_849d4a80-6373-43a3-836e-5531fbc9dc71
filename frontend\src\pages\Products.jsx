import ProductCard from "../ProductCard";

const products = [
  { id: 1, name: "Brass Diya", category: "Lighting", price: 199, image: "/sample/diya.jpg" },
  { id: 2, name: "Incense Sticks", category: "Fragrance", price: 99, image: "/sample/incense.jpg" },
  { id: 3, name: "Lord Ganesha Idol", category: "Idols", price: 499, image: "/sample/ganesha.jpg" },
];

export default function Products() {
  return (
    <section className="container mx-auto px-6 py-16">
      <h2 className="text-3xl font-bold text-gray-900 mb-10 text-center">
        All Products
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {products.map((p) => (
          <ProductCard key={p.id} product={p} />
        ))}
      </div>
    </section>
  );
}
