// src/pages/Products.jsx
import { Link } from "react-router-dom";

const products = [
  {
    id: "1",
    name: "Brass Pooja Thali",
    price: 799,
    image:
      "https://images.unsplash.com/photo-1606312619070-9de36f2e6b78?auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "2",
    name: "Ganesh Idol",
    price: 1299,
    image:
      "https://images.unsplash.com/photo-1598514982903-1c0a219c2f3c?auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "3",
    name: "Decorative Diya Set",
    price: 499,
    image:
      "https://images.unsplash.com/photo-1607522370275-f14206abe5d2?auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "4",
    name: "Marble Hanuman Idol",
    price: 1599,
    image:
      "https://images.unsplash.com/photo-1620742234301-c68dbf86dbe5?auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "5",
    name: "Silver Kumkum Box",
    price: 599,
    image:
      "https://images.unsplash.com/photo-1603448043035-72b6c56d21ab?auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "6",
    name: "Brass Bell",
    price: 299,
    image:
      "https://images.unsplash.com/photo-1616017211332-2b3f0e1d7fa7?auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "7",
    name: "Wooden Incense Holder",
    price: 199,
    image:
      "https://images.unsplash.com/photo-1607538998283-17e0f1c3d3e0?auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "8",
    name: "Crystal Om Pendant",
    price: 899,
    image:
      "https://images.unsplash.com/photo-1620341556436-9f3fa2c07a09?auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "9",
    name: "Decorative Wall Hanging",
    price: 1299,
    image:
      "https://images.unsplash.com/photo-1605692039321-c8f8ed0734fc?auto=format&fit=crop&w=800&q=80",
  },
  {
    id: "10",
    name: "Meditation Cushion",
    price: 699,
    image:
      "https://images.unsplash.com/photo-1617196037890-2a1b2bdbf8a0?auto=format&fit=crop&w=800&q=80",
  },
];

export default function Products() {
  return (
    <div className="max-w-7xl mx-auto px-4 py-10">
      <h2 className="text-3xl font-bold mb-8 text-gray-800 text-center">
        Our Worship Products
      </h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
        {products.map((product) => (
          <Link
            key={product.id}
            to={`/products/${product.id}`} // Navigate to product detail
            className="group block bg-white rounded-lg shadow hover:shadow-lg transition p-4"
          >
            <img
              src={product.image}
              alt={product.name}
              className="h-56 w-full object-cover rounded-md group-hover:scale-105 transition-transform duration-300"
            />
            <h3 className="text-lg font-semibold mt-4 text-gray-700">
              {product.name}
            </h3>
            <p className="text-amber-600 font-bold">₹{product.price}</p>
          </Link>
        ))}
      </div>
    </div>
  );
}
