// src/pages/Products.jsx
import { Link } from "react-router-dom";

const products = [
  {
    id: "1",
    name: "Brass Pooja Thali",
    category: "Pooja Essentials",
    price: 799,
    description: "Handcrafted brass pooja thali for daily rituals.",
    images: [
      "https://images.unsplash.com/photo-1606312619070-9de36f2e6b78?auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1616017211332-2b3f0e1d7fa7?auto=format&fit=crop&w=800&q=80",
    ],
  },
  {
    id: "2",
    name: "Ganesh Idol",
    category: "Idols",
    price: 1299,
    description: "Elegant Ganesh idol made of eco-friendly marble.",
    images: [
      "https://images.unsplash.com/photo-1598514982903-1c0a219c2f3c?auto=format&fit=crop&w=800&q=80",
    ],
  },
  {
    id: "3",
    name: "Decorative Diya Set",
    category: "Lighting",
    price: 499,
    description: "Beautiful diya set for pooja and festive rituals.",
    images: [
      "https://images.unsplash.com/photo-1607522370275-f14206abe5d2?auto=format&fit=crop&w=800&q=80",
    ],
  },
  {
    id: "4",
    name: "Marble Hanuman Idol",
    category: "Idols",
    price: 1599,
    description: "Handcrafted marble Hanuman idol for home temples.",
    images: [
      "https://images.unsplash.com/photo-1620742234301-c68dbf86dbe5?auto=format&fit=crop&w=800&q=80",
    ],
  },
  {
    id: "5",
    name: "Silver Kumkum Box",
    category: "Pooja Essentials",
    price: 599,
    description: "Elegant silver kumkum box for pooja rituals.",
    images: [
      "https://images.unsplash.com/photo-1603448043035-72b6c56d21ab?auto=format&fit=crop&w=800&q=80",
    ],
  },
];

export default function Products() {
  return (
    <div className="max-w-7xl mx-auto px-4 py-10">
      <h2 className="text-3xl font-bold mb-8 text-gray-800 text-center">
        Our Worship Products
      </h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
        {products.map((product) => (
          <Link
            key={product.id}
            to={`/products/${product.id}`} // Navigate to ProductDetail
            className="group block bg-white rounded-lg shadow hover:shadow-lg transition p-4"
          >
            <img
              src={product.images[0]}
              alt={product.name}
              className="h-56 w-full object-cover rounded-md group-hover:scale-105 transition-transform duration-300"
            />
            <h3 className="text-lg font-semibold mt-4 text-gray-700">
              {product.name}
            </h3>
            <p className="text-amber-600 font-bold">₹{product.price}</p>
          </Link>
        ))}
      </div>
    </div>
  );
}

export { products };  