import { <PERSON>aFacebookF, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaYoutube } from "react-icons/fa";

export default function Footer() {
  return (
    <footer className="bg-gradient-to-br from-gray-900 via-gray-950 to-black text-gray-300">
      <div className="container mx-auto px-6 py-16 grid grid-cols-1 md:grid-cols-4 gap-12">

        {/* Brand & Tagline */}
        <div>
          <h2 className="text-3xl font-extrabold text-yellow-400 tracking-wide">
            Worship<span className="text-white">Mart</span>
          </h2>
          <p className="mt-4 text-sm leading-relaxed text-gray-400">
            Sacred essentials delivered with devotion — Idols, <PERSON>yas, Puja <PERSON>, 
            and everything that connects you spiritually.
          </p>
        </div>

        {/* Quick Links */}
        <div>
          <h3 className="text-lg font-semibold text-white mb-4">Quick Links</h3>
          <ul className="space-y-3 text-sm">
            <li><a href="/products" className="hover:text-yellow-400 transition">All Products</a></li>
            <li><a href="/about" className="hover:text-yellow-400 transition">About Us</a></li>
            <li><a href="/offers" className="hover:text-yellow-400 transition">Special Offers</a></li>
            <li><a href="/blog" className="hover:text-yellow-400 transition">Blog</a></li>
          </ul>
        </div>

        {/* Customer Support */}
        <div>
          <h3 className="text-lg font-semibold text-white mb-4">Customer Support</h3>
          <ul className="space-y-3 text-sm">
            <li><a href="/help" className="hover:text-yellow-400 transition">Help Center</a></li>
            <li><a href="/shipping" className="hover:text-yellow-400 transition">Shipping & Delivery</a></li>
            <li><a href="/returns" className="hover:text-yellow-400 transition">Returns & Refunds</a></li>
            <li><a href="/contact" className="hover:text-yellow-400 transition">Contact Us</a></li>
          </ul>
        </div>

        {/* Newsletter & Social */}
        <div>
          <h3 className="text-lg font-semibold text-white mb-4">Stay Connected</h3>
          <p className="text-sm mb-4 text-gray-400">Subscribe to receive updates & exclusive deals:</p>
          <div className="flex items-center">
            <input
              type="email"
              placeholder="Enter your email"
              className="px-3 py-2 rounded-l-lg w-full text-sm focus:outline-none focus:ring-2 focus:ring-yellow-400"
            />
            <button className="bg-yellow-400 text-black px-4 py-2 rounded-r-lg font-medium hover:bg-yellow-500 transition">
              Subscribe
            </button>
          </div>

          <div className="flex space-x-5 mt-6">
            <a href="#" className="hover:text-yellow-400 transition"><FaFacebookF size={18} /></a>
            <a href="#" className="hover:text-yellow-400 transition"><FaInstagram size={18} /></a>
            <a href="#" className="hover:text-yellow-400 transition"><FaTwitter size={18} /></a>
            <a href="#" className="hover:text-yellow-400 transition"><FaYoutube size={18} /></a>
          </div>
        </div>
      </div>

      {/* Divider */}
      <div className="border-t border-gray-800 mt-10 py-6 text-center text-sm text-gray-500">
        © {new Date().getFullYear()} WorshipMart. All Rights Reserved.
      </div>
    </footer>
  );
}
